#!/usr/bin/env python3
"""
Braintrust Prompt Manager - A versatile utility for managing prompts in Braintrust.

Features:
- List all prompts in a project
- Pull specific prompts with version history
- Push new versions of prompts using Python SDK
- Maintain local records of prompt versions
- Compare prompt versions
"""

import argparse
import json
import logging
import os
import re
import sys
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import requests

# Import Braintrust SDK
try:
    import braintrust
except ImportError:
    print("Error: braintrust package not found. Install with: pip install braintrust")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


def get_api_key() -> str:
    """Get Braintrust API key from environment variable."""
    api_key = os.environ.get("BRAINTRUST_API_KEY")
    if not api_key:
        logger.error("BRAINTRUST_API_KEY environment variable not set")
        sys.exit(1)
    return api_key


def create_prompts_folder() -> str:
    """Create the prompts folder if it doesn't exist."""
    root_dir = os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    )
    prompts_dir = os.path.join(root_dir, "braintrust_prompts")

    if not os.path.exists(prompts_dir):
        try:
            os.makedirs(prompts_dir)
            logger.info(f"Created prompts directory at {prompts_dir}")
        except Exception as e:
            logger.error(f"Error creating prompts directory: {e}")
            prompts_dir = "."

    return prompts_dir


def list_prompts(project_id: str, api_key: str) -> List[Dict[str, Any]]:
    """
    List all prompts in a project.

    Args:
        project_id: Braintrust project ID
        api_key: Braintrust API key

    Returns:
        List of prompt objects
    """
    url = f"https://api.braintrust.dev/v1/prompt?project_id={project_id}"
    headers = {"Authorization": f"Bearer {api_key}"}

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data: Dict[str, Any] = response.json()

        prompts: List[Dict[str, Any]] = data.get("objects", [])
        logger.info(f"Found {len(prompts)} prompts in project")

        return prompts
    except requests.exceptions.RequestException as e:
        logger.error(f"Error listing prompts: {e}")
        if hasattr(e, "response") and e.response:
            logger.error(f"Response: {e.response.text}")
        return []


def get_prompt_by_id(prompt_id: str, api_key: str) -> Optional[Dict[str, Any]]:
    """
    Get a specific prompt by ID with all its details.

    Args:
        prompt_id: The prompt ID
        api_key: Braintrust API key

    Returns:
        Prompt object with full details
    """
    url = f"https://api.braintrust.dev/v1/prompt/{prompt_id}"
    headers = {"Authorization": f"Bearer {api_key}"}

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        prompt_data: Dict[str, Any] = response.json()

        version_id = prompt_data.get("_xact_id", "N/A")
        logger.info(
            f"Retrieved prompt: {prompt_data.get('name', 'Unknown')} (version: {version_id})"
        )
        return prompt_data
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching prompt {prompt_id}: {e}")
        if hasattr(e, "response") and e.response:
            logger.error(f"Response: {e.response.text}")
        return None


def check_if_prompt_is_pinned(
    prompt_slug: str,
) -> Tuple[bool, Optional[str], Optional[str]]:
    """
    Check if a prompt is pinned by looking at the braintrust_client.py file.

    Args:
        prompt_slug: The slug of the prompt to check

    Returns:
        Tuple of (is_pinned, current_version_id, enum_name)
    """
    try:
        braintrust_client_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            "app",
            "utils",
            "llm_utils",
            "braintrust_client.py",
        )

        if not os.path.exists(braintrust_client_path):
            logger.warning(
                f"braintrust_client.py not found at {braintrust_client_path}"
            )
            return False, None, None

        with open(braintrust_client_path, "r") as f:
            content = f.read()

        # Look for the prompt in the enum
        # Handle both single-line and multi-line enum definitions
        # Pattern 1: ENUM_NAME = "slug"
        # Pattern 2: ENUM_NAME = (\n    "slug"\n)
        enum_pattern = rf'(\w+)\s*=\s*(?:\(\s*\n\s*)?["\']({re.escape(prompt_slug)})["\'](?:\s*\n\s*\))?'
        enum_match = re.search(enum_pattern, content, re.MULTILINE | re.DOTALL)

        if not enum_match:
            logger.info(
                f"Prompt slug '{prompt_slug}' not found in BraintrustPrompt enum"
            )
            return False, None, None

        enum_name = enum_match.group(1)

        # Look for the enum in PINNED_VERSIONS
        pinned_pattern = rf'BraintrustPrompt\.{enum_name}:\s*["\']([^"\']+)["\']'
        pinned_match = re.search(pinned_pattern, content)

        if pinned_match:
            current_version_id = pinned_match.group(1)
            logger.info(
                f"✅ Prompt '{prompt_slug}' is pinned as {enum_name} with version {current_version_id}"
            )
            return True, current_version_id, enum_name
        else:
            logger.info(
                f"ℹ️  Prompt '{prompt_slug}' exists in enum as {enum_name} but is not pinned"
            )
            return False, None, enum_name

    except Exception as e:
        logger.error(f"Error checking if prompt is pinned: {e}")
        return False, None, None


def update_pinned_version(
    prompt_slug: str, new_version_id: str, enum_name: str
) -> bool:
    """
    Update the pinned version in braintrust_client.py for a specific prompt.

    Args:
        prompt_slug: The slug of the prompt
        new_version_id: The new version ID to pin
        enum_name: The enum name for the prompt

    Returns:
        bool: Success status
    """
    try:
        braintrust_client_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            "app",
            "utils",
            "llm_utils",
            "braintrust_client.py",
        )

        with open(braintrust_client_path, "r") as f:
            content = f.read()

        # Pattern to match the specific pinned version line
        pattern = rf'(BraintrustPrompt\.{enum_name}:\s*["\'])([^"\']+)(["\'])'

        def replacer(match):
            return f"{match.group(1)}{new_version_id}{match.group(3)}"

        new_content = re.sub(pattern, replacer, content)

        if new_content == content:
            logger.warning(f"No pinned version found to update for {enum_name}")
            return False

        # Write back the updated content
        with open(braintrust_client_path, "w") as f:
            f.write(new_content)

        logger.info(f"✅ Updated pinned version for {enum_name} to {new_version_id}")
        return True

    except Exception as e:
        logger.error(f"Error updating pinned version: {e}")
        return False


def convert_prompt_data_for_sdk(prompt_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert prompt data from file format to SDK format.

    Args:
        prompt_data: Prompt data from JSON file

    Returns:
        Converted prompt data for SDK
    """
    # Handle nested prompt_data structure
    if "prompt_data" in prompt_data:
        inner_data = prompt_data["prompt_data"]
        if "prompt" in inner_data:
            actual_prompt = inner_data["prompt"]
        else:
            actual_prompt = inner_data
    else:
        actual_prompt = prompt_data

    # Extract the key components
    converted = {
        "name": prompt_data.get("name", "Unknown Prompt"),
        "messages": actual_prompt.get("messages", []),
        "model": actual_prompt.get("options", {}).get("model", "gpt-4o"),
        "tools": actual_prompt.get("tools", []),
    }

    return converted


def create_prompt_version_sdk(
    project_name: str,
    prompt_slug: str,
    prompt_data: Dict[str, Any],
    description: Optional[str] = None,
) -> Optional[Dict[str, Any]]:
    """
    Create a new version of an existing prompt using the Braintrust Python SDK.

    Args:
        project_name: Braintrust project name
        prompt_slug: The prompt slug (identifier)
        prompt_data: The prompt configuration (messages, model, etc.)
        description: Optional description for this version

    Returns:
        Created prompt version object
    """
    # Check if this prompt is pinned before making changes
    is_pinned, current_version, enum_name = check_if_prompt_is_pinned(prompt_slug)

    try:
        # Initialize Braintrust project
        project = braintrust.projects.create(name=project_name)

        # Convert prompt data to SDK format
        converted_data = convert_prompt_data_for_sdk(prompt_data)

        # Extract prompt configuration
        name = converted_data.get("name", prompt_slug)
        model = converted_data.get("model", "gpt-4o")
        messages = converted_data.get("messages", [])
        tools = converted_data.get("tools", [])

        # Create the prompt
        logger.info(f"Creating prompt '{name}' with slug '{prompt_slug}'...")

        prompt_args = {
            "name": name,
            "slug": prompt_slug,
            "model": model,
            "messages": messages,
        }

        if description:
            prompt_args["description"] = description

        if tools:
            prompt_args["tools"] = tools

        project.prompts.create(**prompt_args)

        # Publish the prompt to Braintrust
        logger.info("Publishing prompt to Braintrust...")
        project.publish()

        logger.info(f"✅ Successfully created/updated prompt: {prompt_slug}")

        # Get the created prompt details (we need to fetch it back to get version info)
        # For now, we'll create a mock response similar to the API format
        created_prompt = {
            "name": name,
            "slug": prompt_slug,
            "model": model,
            "messages": messages,
            "description": description,
            "id": "sdk-created",  # We don't get the actual ID from SDK
            "_xact_id": "sdk-version",  # We don't get the actual version from SDK
        }

        # Update pinned version if this prompt is pinned
        if is_pinned and enum_name:
            logger.info(f"🔗 Prompt is pinned as {enum_name}")
            logger.info(
                "ℹ️  Note: When using SDK, you'll need to manually update the pinned version"
            )
            logger.info(
                "ℹ️  Run 'pull' command to get the new version ID, then update braintrust_client.py"
            )
        elif enum_name and not is_pinned:
            logger.info(
                f"ℹ️  Prompt exists in enum as {enum_name} but is not pinned - no version update needed"
            )
        else:
            logger.info("ℹ️  Prompt is not pinned - no version update needed")

        return created_prompt

    except Exception as e:
        logger.error(f"Error creating prompt version with SDK: {e}")
        return None


def create_prompt_version(
    project_id: str,
    prompt_slug: str,
    prompt_data: Dict[str, Any],
    api_key: str,
    description: Optional[str] = None,
) -> Optional[Dict[str, Any]]:
    """
    Create a new version of an existing prompt using REST API.

    DEPRECATED: Use create_prompt_version_sdk instead for better reliability.

    Args:
        project_id: Braintrust project ID
        prompt_slug: The prompt slug (identifier)
        prompt_data: The prompt configuration (messages, model, etc.)
        api_key: Braintrust API key
        description: Optional description for this version

    Returns:
        Created prompt version object
    """
    url = "https://api.braintrust.dev/v1/prompt"
    headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}

    payload = {"project_id": project_id, "slug": prompt_slug, **prompt_data}

    if description:
        payload["description"] = description

    # Check if this prompt is pinned before making changes
    is_pinned, current_version, enum_name = check_if_prompt_is_pinned(prompt_slug)

    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        created_prompt: Dict[str, Any] = response.json()
        new_version_id = created_prompt.get("_xact_id") or created_prompt.get("version")

        logger.info(f"Created new version of prompt: {prompt_slug}")
        logger.info(f"New version ID: {new_version_id}")
        logger.info(f"Prompt ID: {created_prompt.get('id', 'N/A')}")

        # Update pinned version if this prompt is pinned
        if is_pinned and enum_name and new_version_id:
            logger.info(f"🔗 Updating pinned version for {enum_name}...")
            if update_pinned_version(prompt_slug, new_version_id, enum_name):
                logger.info("✅ Updated pinned version in braintrust_client.py")
            else:
                logger.warning(
                    "⚠️  Failed to update pinned version in braintrust_client.py"
                )
        elif enum_name and not is_pinned:
            logger.info(
                f"ℹ️  Prompt exists in enum as {enum_name} but is not pinned - no version update needed"
            )
        else:
            logger.info("ℹ️  Prompt is not pinned - no version update needed")

        return created_prompt
    except requests.exceptions.RequestException as e:
        logger.error(f"Error creating prompt version: {e}")
        if hasattr(e, "response") and e.response:
            logger.error(f"Response: {e.response.text}")
        return None


def save_prompt_to_file(
    prompt_data: Dict[str, Any], prompts_dir: str, version_suffix: Optional[str] = None
) -> str:
    """
    Save prompt data to a JSON file with descriptive name.

    Args:
        prompt_data: The prompt data to save
        prompts_dir: Directory to save prompts in
        version_suffix: Optional suffix for versioning

    Returns:
        Path to saved file
    """
    try:
        slug = prompt_data.get("slug", "unknown_prompt")
        name = prompt_data.get("name", slug)

        # Clean name for filename
        clean_name = re.sub(r"[^\w\s-]", "", name).strip().lower()
        clean_name = re.sub(r"[-\s]+", "_", clean_name)

        # Add timestamp and version if provided
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if version_suffix:
            filename = f"{clean_name}_{version_suffix}_{timestamp}.json"
        else:
            filename = f"{clean_name}_{timestamp}.json"

        filepath = os.path.join(prompts_dir, filename)

        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(prompt_data, f, indent=2, ensure_ascii=False)

        logger.info(f"Prompt saved to {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"Error saving prompt to file: {e}")
        raise


def load_prompt_from_file(filepath: str) -> Optional[Dict[str, Any]]:
    """
    Load prompt data from a JSON file.

    Args:
        filepath: Path to the prompt JSON file

    Returns:
        Prompt data dictionary
    """
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            return json.load(f)  # type: ignore[no-any-return]
    except Exception as e:
        logger.error(f"Error loading prompt from file {filepath}: {e}")
        return None


def compare_prompts(
    prompt1: Dict[str, Any], prompt2: Dict[str, Any]
) -> Dict[str, bool]:
    """
    Compare two prompt versions and highlight differences.

    Args:
        prompt1: First prompt version
        prompt2: Second prompt version

    Returns:
        Dictionary with comparison results
    """
    differences = {
        "model_changed": prompt1.get("model") != prompt2.get("model"),
        "messages_changed": prompt1.get("messages") != prompt2.get("messages"),
        "parameters_changed": prompt1.get("parameters") != prompt2.get("parameters"),
        "name_changed": prompt1.get("name") != prompt2.get("name"),
        "description_changed": prompt1.get("description") != prompt2.get("description"),
    }

    differences["any_changes"] = any(differences.values())

    return differences


def interactive_prompt_editor(current_prompt: Dict[str, Any]) -> Dict[str, Any]:
    """
    Interactive editor for modifying prompt content.

    Args:
        current_prompt: Current prompt data

    Returns:
        Modified prompt data
    """
    print("\n=== Interactive Prompt Editor ===")
    print(f"Current prompt: {current_prompt.get('name', 'Unknown')}")
    print(f"Model: {current_prompt.get('model', 'Unknown')}")

    # Allow editing of key fields
    new_prompt = current_prompt.copy()

    # Edit name
    current_name = current_prompt.get("name", "")
    new_name = input(f"Name [{current_name}]: ").strip()
    if new_name:
        new_prompt["name"] = new_name

    # Edit description
    current_desc = current_prompt.get("description", "")
    new_desc = input(f"Description [{current_desc}]: ").strip()
    if new_desc:
        new_prompt["description"] = new_desc

    # Edit model
    current_model = current_prompt.get("model", "")
    new_model = input(f"Model [{current_model}]: ").strip()
    if new_model:
        new_prompt["model"] = new_model

    # Edit messages (simplified - in practice you might want a more sophisticated editor)
    print("\nCurrent messages:")
    messages = current_prompt.get("messages", [])
    for i, msg in enumerate(messages):
        print(
            f"  {i + 1}. {msg.get('role', 'unknown')}: {msg.get('content', '')[:100]}..."
        )

    edit_messages = input("Edit messages? (y/N): ").lower().startswith("y")
    if edit_messages:
        print(
            "Note: For complex message editing, consider editing the JSON file directly"
        )
        print("Current implementation supports simple text replacement only")

        for i, msg in enumerate(messages):
            if msg.get("role") == "system":
                current_content = msg.get("content", "")
                print(f"\nSystem message {i + 1}:")
                print(f"Current: {current_content}")
                new_content = input(
                    "New content (or press Enter to keep current): "
                ).strip()
                if new_content:
                    new_prompt["messages"][i]["content"] = new_content

    return new_prompt


def main() -> None:
    parser = argparse.ArgumentParser(
        description="Braintrust Prompt Manager - Manage prompts and their versions"
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # List command
    list_parser = subparsers.add_parser("list", help="List all prompts in project")
    list_parser.add_argument(
        "--project-id",
        default="5d78e5e9-40a6-4c6c-8ac6-9b4bd11f5713",
        help="Braintrust project ID",
    )

    # Pull command
    pull_parser = subparsers.add_parser("pull", help="Pull a specific prompt")
    pull_parser.add_argument("prompt_id", help="Prompt ID to pull")
    pull_parser.add_argument(
        "--save", action="store_true", help="Save prompt to local file"
    )

    # Push command
    push_parser = subparsers.add_parser("push", help="Push a new prompt version")
    push_parser.add_argument(
        "--project-id",
        default="5d78e5e9-40a6-4c6c-8ac6-9b4bd11f5713",
        help="Braintrust project ID (for REST API)",
    )
    push_parser.add_argument(
        "--project-name",
        default="Alma",
        help="Braintrust project name (for SDK)",
    )
    push_parser.add_argument("--slug", required=True, help="Prompt slug (identifier)")
    push_parser.add_argument("--file", help="JSON file containing prompt data")
    push_parser.add_argument("--description", help="Description for this version")
    push_parser.add_argument(
        "--interactive", action="store_true", help="Use interactive editor"
    )
    push_parser.add_argument(
        "--use-sdk",
        action="store_true",
        help="Use Python SDK instead of REST API (recommended)",
    )

    # Compare command
    compare_parser = subparsers.add_parser("compare", help="Compare two prompt files")
    compare_parser.add_argument("file1", help="First prompt file")
    compare_parser.add_argument("file2", help="Second prompt file")

    # Pinned command
    pinned_parser = subparsers.add_parser("pinned", help="Check if a prompt is pinned")
    pinned_parser.add_argument("slug", help="Prompt slug to check")

    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    if args.debug:
        logger.setLevel(logging.DEBUG)

    if not args.command:
        parser.print_help()
        return

    api_key = get_api_key()
    prompts_dir = create_prompts_folder()

    if args.command == "list":
        prompts = list_prompts(args.project_id, api_key)
        if prompts:
            print(f"\n=== Prompts in Project {args.project_id} ===")
            for prompt in prompts:
                print(f"ID: {prompt.get('id', 'N/A')}")
                print(f"Name: {prompt.get('name', 'N/A')}")
                print(f"Slug: {prompt.get('slug', 'N/A')}")
                print(f"Model: {prompt.get('model', 'N/A')}")
                print(f"Created: {prompt.get('created', 'N/A')}")
                print("-" * 50)

    elif args.command == "pull":
        prompt_data = get_prompt_by_id(args.prompt_id, api_key)
        if prompt_data:
            print("\n=== Prompt Details ===")
            print(f"Name: {prompt_data.get('name', 'N/A')}")
            print(f"Slug: {prompt_data.get('slug', 'N/A')}")
            print(f"Model: {prompt_data.get('model', 'N/A')}")
            print(f"Description: {prompt_data.get('description', 'N/A')}")

            if args.save:
                filepath = save_prompt_to_file(prompt_data, prompts_dir, "pulled")
                print(f"\nPrompt saved to: {filepath}")

    elif args.command == "push":
        prompt_data = None

        if args.file:
            prompt_data = load_prompt_from_file(args.file)
            if not prompt_data:
                logger.error(f"Failed to load prompt from {args.file}")
                return

        if args.interactive:
            if prompt_data:
                prompt_data = interactive_prompt_editor(prompt_data)
            else:
                logger.error("Interactive mode requires --file argument")
                return

        if prompt_data:
            # Save a backup before pushing
            backup_path = save_prompt_to_file(prompt_data, prompts_dir, "pre_push")
            logger.info(f"Backup saved to: {backup_path}")

            # Choose SDK or REST API based on flag
            if args.use_sdk:
                logger.info("🚀 Using Braintrust Python SDK (recommended)")
                result = create_prompt_version_sdk(
                    args.project_name, args.slug, prompt_data, args.description
                )
            else:
                logger.info("⚠️  Using REST API (deprecated - consider using --use-sdk)")
                result = create_prompt_version(
                    args.project_id, args.slug, prompt_data, api_key, args.description
                )

            if result:
                # Save the result
                result_path = save_prompt_to_file(result, prompts_dir, "pushed")
                logger.info(f"New version saved to: {result_path}")
                print("\n✅ Successfully created new prompt version!")
                print(f"Prompt ID: {result.get('id', 'N/A')}")
                print(
                    f"Version: {result.get('_xact_id', result.get('version', 'N/A'))}"
                )

                if args.use_sdk:
                    print("\n📝 Next steps:")
                    print("1. Run the 'pull' command to get the actual version ID")
                    print(
                        "2. Update braintrust_client.py with the new version if needed"
                    )
        else:
            logger.error("No prompt data provided. Use --file or --interactive")

    elif args.command == "compare":
        prompt1 = load_prompt_from_file(args.file1)
        prompt2 = load_prompt_from_file(args.file2)

        if prompt1 and prompt2:
            differences = compare_prompts(prompt1, prompt2)

            print("\n=== Comparison Results ===")
            print(f"File 1: {args.file1}")
            print(f"File 2: {args.file2}")
            print(f"\nChanges detected: {differences['any_changes']}")

            if differences["any_changes"]:
                if differences["model_changed"]:
                    print(f"Model: {prompt1.get('model')} → {prompt2.get('model')}")
                if differences["name_changed"]:
                    print(f"Name: {prompt1.get('name')} → {prompt2.get('name')}")
                if differences["description_changed"]:
                    print("Description changed")
                if differences["messages_changed"]:
                    print("Messages changed")
                if differences["parameters_changed"]:
                    print("Parameters changed")
            else:
                print("No differences found.")

    elif args.command == "pinned":
        is_pinned, current_version, enum_name = check_if_prompt_is_pinned(args.slug)

        print(f"\n=== Pinned Status for '{args.slug}' ===")

        if enum_name:
            print(f"Enum name: {enum_name}")
            print(f"Is pinned: {'Yes' if is_pinned else 'No'}")

            if is_pinned and current_version:
                print(f"Current pinned version: {current_version}")
                print(
                    "✅ This prompt will have its pinned version automatically updated when you push changes"
                )
            elif enum_name:
                print("ℹ️  This prompt exists in the enum but is not currently pinned")
            else:
                print("❌ This prompt is not found in the BraintrustPrompt enum")
        else:
            print("❌ This prompt is not found in the BraintrustPrompt enum")
            print("ℹ️  Changes to this prompt will not update any pinned versions")


if __name__ == "__main__":
    main()
